# Cloud File Manager Setup Guide

## Overview

Your Super App now includes a comprehensive cloud file manager that stores user files on Firebase Storage instead of localStorage. This provides better scalability, cross-device access, and proper file management.

## What's New

### ✅ Cloud Storage Integration
- Files are now stored in Firebase Storage
- Each user has their own isolated storage space
- Automatic file organization and management

### ✅ Offline Support
- Files are cached locally for offline access
- Pending uploads are queued when offline
- Automatic sync when connection is restored

### ✅ Storage Quota Management
- Real-time storage usage tracking
- Visual quota indicators
- Automatic cleanup utilities

### ✅ Enhanced File Operations
- Improved upload progress tracking
- Better error handling
- Support for large files

## Setup Instructions

### 1. Firebase Configuration

Your Firebase is already configured, but ensure you have Firebase Storage enabled:

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `super-app-54ae9`
3. Navigate to **Storage** in the left sidebar
4. Click **Get Started** if not already enabled
5. Choose **Start in test mode** for development

### 2. Storage Rules (Important!)

Update your Firebase Storage rules for security:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can only access their own files
    match /users/{userId}/files/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 3. Environment Variables

Your `.env` file should include:

```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=super-app-54ae9.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=super-app-54ae9
VITE_FIREBASE_STORAGE_BUCKET=super-app-54ae9.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=************
VITE_FIREBASE_APP_ID=your_app_id
```

## Features

### File Upload
- **Cloud Storage**: Files are uploaded to Firebase Storage
- **Progress Tracking**: Real-time upload progress
- **Offline Queue**: Files are queued for upload when offline
- **Error Handling**: Graceful fallback to local storage if needed

### File Management
- **Folder Organization**: Create and manage folders
- **Search**: Search through files and folders
- **Preview**: Preview images and text files
- **Download**: Download files from cloud storage

### Storage Monitoring
- **Usage Tracking**: Real-time storage usage display
- **Quota Management**: Visual indicators for storage limits
- **Cleanup Tools**: Automatic cleanup of old files

### Offline Support
- **File Caching**: Recently accessed files are cached
- **Offline Access**: View cached files when offline
- **Auto Sync**: Pending uploads sync when online

## Usage

### For Users

1. **Upload Files**: Click "Upload Files" button or drag & drop
2. **Create Folders**: Click "New Folder" to organize files
3. **Search**: Use the search bar to find files quickly
4. **Monitor Storage**: Check the storage quota indicator
5. **Offline Access**: Files remain accessible when offline

### For Developers

#### Upload a File
```typescript
import { storageUtils } from './utils/storage';

const uploadFile = async (file: File, userId: string) => {
  const result = await storageUtils.uploadFileToCloud(
    userId,
    file,
    parentFolderId, // optional
    (progress) => console.log(`Upload progress: ${progress}%`)
  );
  
  if (result) {
    console.log('File uploaded successfully:', result);
  }
};
```

#### Get File Content
```typescript
const getFileContent = async (file: FileItem) => {
  const content = await storageUtils.getFileContent(file);
  if (content) {
    // Use the content (URL for cloud files, base64 for legacy files)
    console.log('File content:', content);
  }
};
```

#### Check Storage Usage
```typescript
const checkStorage = async (userId: string) => {
  const usage = await storageUtils.getUserStorageUsage(userId);
  console.log(`Storage: ${usage.usage}/${usage.quota} bytes (${usage.percentage}%)`);
};
```

## File Structure

```
src/
├── utils/
│   ├── cloudStorage.ts      # Firebase Storage service
│   ├── offlineSync.ts       # Offline support and caching
│   └── storage.ts           # Updated storage utilities
├── components/
│   ├── FileManager.tsx      # Enhanced file manager
│   ├── FilePreview.tsx      # Updated file preview
│   └── StorageQuota.tsx     # Storage usage component
└── types/
    └── index.ts             # Updated FileItem interface
```

## Migration

### Existing Files
- Legacy files (base64 in localStorage) continue to work
- New files are automatically stored in cloud
- Use `migrateLocalFilesToCloud()` to migrate existing files

### Backward Compatibility
- All existing functionality is preserved
- Gradual migration to cloud storage
- Fallback to local storage if cloud fails

## Storage Limits

- **Default Quota**: 100MB per user
- **File Size Limit**: 10MB per file (configurable)
- **File Types**: PDF, PPT, DOC, images, text files
- **Cleanup**: Automatic cleanup of files older than 30 days (configurable)

## Troubleshooting

### Upload Failures
1. Check internet connection
2. Verify Firebase Storage rules
3. Check file size limits
4. Review browser console for errors

### Storage Quota Issues
1. Use storage cleanup utilities
2. Delete unnecessary files
3. Increase quota limits if needed

### Offline Issues
1. Clear browser cache if needed
2. Check localStorage quota
3. Verify offline sync is working

## Next Steps

1. **Test the file upload functionality**
2. **Configure Firebase Storage rules**
3. **Set up monitoring for storage usage**
4. **Consider implementing file sharing features**
5. **Add file versioning if needed**

## Support

For issues or questions:
1. Check browser console for errors
2. Verify Firebase configuration
3. Test with different file types and sizes
4. Monitor network requests in browser dev tools
