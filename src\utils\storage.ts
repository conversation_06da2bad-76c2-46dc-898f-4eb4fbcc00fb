import { FileItem, Task, Note, AIAnalysis } from "../types";
import { cloudStorageService } from "./cloudStorage";
import { offlineSyncService } from "./offlineSync";

const FILES_KEY = "super_study_files";
const TASKS_KEY = "super_study_tasks";
const NOTES_KEY = "super_study_notes";
const AI_ANALYSIS_KEY = "super_study_ai_analysis";

export const storageUtils = {
  generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // File Management
  getFiles(userId: string): FileItem[] {
    const files = localStorage.getItem(FILES_KEY);
    const allFiles: FileItem[] = files ? JSON.parse(files) : [];
    return allFiles.filter((file) => file.userId === userId);
  },

  storeFile(file: FileItem): void {
    const files = JSON.parse(localStorage.getItem(FILES_KEY) || "[]");
    files.push(file);
    localStorage.setItem(FILES_KEY, JSON.stringify(files));
  },

  updateFile(fileId: string, updates: Partial<FileItem>): void {
    const files = JSON.parse(localStorage.getItem(FILES_KEY) || "[]");
    const index = files.findIndex((f: FileItem) => f.id === fileId);
    if (index !== -1) {
      files[index] = { ...files[index], ...updates };
      localStorage.setItem(FILES_KEY, JSON.stringify(files));
    }
  },

  async deleteFile(fileId: string): Promise<void> {
    const files = JSON.parse(localStorage.getItem(FILES_KEY) || "[]");
    const fileToDelete = files.find((f: FileItem) => f.id === fileId);

    // If it's a cloud file, delete from Firebase Storage
    if (fileToDelete?.isCloudFile && fileToDelete.cloudPath) {
      await cloudStorageService.deleteFile(fileToDelete.cloudPath);
    }

    // Remove from local storage
    const filtered = files.filter((f: FileItem) => f.id !== fileId);
    localStorage.setItem(FILES_KEY, JSON.stringify(filtered));
  },

  // Task Management
  getTasks(userId: string): Task[] {
    const tasks = localStorage.getItem(TASKS_KEY);
    const allTasks: Task[] = tasks ? JSON.parse(tasks) : [];
    return allTasks.filter((task) => task.userId === userId);
  },

  storeTask(task: Task): void {
    const tasks = JSON.parse(localStorage.getItem(TASKS_KEY) || "[]");
    tasks.push(task);
    localStorage.setItem(TASKS_KEY, JSON.stringify(tasks));
  },

  updateTask(taskId: string, updates: Partial<Task>): void {
    const tasks = JSON.parse(localStorage.getItem(TASKS_KEY) || "[]");
    const index = tasks.findIndex((t: Task) => t.id === taskId);
    if (index !== -1) {
      tasks[index] = { ...tasks[index], ...updates };
      localStorage.setItem(TASKS_KEY, JSON.stringify(tasks));
    }
  },

  deleteTask(taskId: string): void {
    const tasks = JSON.parse(localStorage.getItem(TASKS_KEY) || "[]");
    const filtered = tasks.filter((t: Task) => t.id !== taskId);
    localStorage.setItem(TASKS_KEY, JSON.stringify(filtered));
  },

  // Notes Management
  getNotes(userId: string): Note[] {
    const notes = localStorage.getItem(NOTES_KEY);
    const allNotes: Note[] = notes ? JSON.parse(notes) : [];
    return allNotes.filter((note) => note.userId === userId);
  },

  storeNote(note: Note): void {
    const notes = JSON.parse(localStorage.getItem(NOTES_KEY) || "[]");
    notes.push(note);
    localStorage.setItem(NOTES_KEY, JSON.stringify(notes));
  },

  updateNote(noteId: string, updates: Partial<Note>): void {
    const notes = JSON.parse(localStorage.getItem(NOTES_KEY) || "[]");
    const index = notes.findIndex((n: Note) => n.id === noteId);
    if (index !== -1) {
      notes[index] = {
        ...notes[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      localStorage.setItem(NOTES_KEY, JSON.stringify(notes));
    }
  },

  deleteNote(noteId: string): void {
    const notes = JSON.parse(localStorage.getItem(NOTES_KEY) || "[]");
    const filtered = notes.filter((n: Note) => n.id !== noteId);
    localStorage.setItem(NOTES_KEY, JSON.stringify(filtered));
  },

  // AI Analysis
  getAIAnalysis(userId: string): AIAnalysis[] {
    const analysis = localStorage.getItem(AI_ANALYSIS_KEY);
    const allAnalysis: AIAnalysis[] = analysis ? JSON.parse(analysis) : [];
    return allAnalysis.filter((item) => item.userId === userId);
  },

  storeAIAnalysis(analysis: AIAnalysis): void {
    const analyses = JSON.parse(localStorage.getItem(AI_ANALYSIS_KEY) || "[]");
    analyses.push(analysis);
    localStorage.setItem(AI_ANALYSIS_KEY, JSON.stringify(analyses));
  },

  // Cloud File Management
  async uploadFileToCloud(
    userId: string,
    file: File,
    parentId?: string,
    onProgress?: (progress: number) => void
  ): Promise<FileItem | null> {
    try {
      // Check if online
      if (!offlineSyncService.getOnlineStatus()) {
        // Add to pending uploads for offline sync
        const uploadId = offlineSyncService.addPendingUpload(
          file,
          userId,
          parentId
        );

        // Create a temporary file item for immediate UI feedback
        const tempFileItem: FileItem = {
          id: this.generateId(),
          name: file.name,
          type: "file",
          mimeType: file.type,
          size: file.size,
          parentId,
          uploadedAt: new Date().toISOString(),
          userId,
          isCloudFile: false, // Will be updated when synced
          content: `pending_upload_${uploadId}`, // Temporary marker
        };

        this.storeFile(tempFileItem);
        return tempFileItem;
      }

      const result = await cloudStorageService.uploadFile(
        userId,
        file,
        parentId || "",
        (progress) => onProgress?.(progress.progress)
      );

      if (result.success && result.downloadURL && result.metadata) {
        const fileItem: FileItem = {
          id: this.generateId(),
          name: file.name,
          type: "file",
          mimeType: file.type,
          size: file.size,
          parentId,
          cloudPath: result.metadata.fullPath,
          downloadURL: result.downloadURL,
          uploadedAt: new Date().toISOString(),
          userId,
          isCloudFile: true,
        };

        this.storeFile(fileItem);
        return fileItem;
      }
      return null;
    } catch (error) {
      console.error("Error uploading file to cloud:", error);
      return null;
    }
  },

  async getFileContent(file: FileItem): Promise<string | null> {
    // Check for pending uploads
    if (file.content?.startsWith("pending_upload_")) {
      return null; // File is still being uploaded
    }

    // For legacy files with base64 content
    if (file.content && !file.isCloudFile) {
      return file.content;
    }

    // For cloud files, try cache first if offline
    if (file.isCloudFile) {
      // Check cache first (especially useful when offline)
      const cachedContent = offlineSyncService.getCachedFile(file.id);
      if (cachedContent) {
        return cachedContent;
      }

      // If online, get from cloud and cache it
      if (offlineSyncService.getOnlineStatus()) {
        if (file.downloadURL) {
          // Cache the download URL for offline access
          offlineSyncService.cacheFile(file.id, file.downloadURL);
          return file.downloadURL;
        }

        // Try to get fresh download URL if needed
        if (file.cloudPath) {
          const downloadURL = await cloudStorageService.downloadFile(
            file.cloudPath
          );
          if (downloadURL) {
            // Update the file record with fresh URL
            this.updateFile(file.id, { downloadURL });
            // Cache it for offline access
            offlineSyncService.cacheFile(file.id, downloadURL);
            return downloadURL;
          }
        }
      }
    }

    return null;
  },

  async getUserStorageUsage(userId: string): Promise<{
    usage: number;
    quota: number;
    exceeded: boolean;
    percentage: number;
  }> {
    return await cloudStorageService.checkStorageQuota(userId);
  },

  // Storage Management and Cleanup
  async cleanupOldFiles(userId: string, daysOld: number = 30): Promise<number> {
    try {
      const files = this.getFiles(userId);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      let deletedCount = 0;

      for (const file of files) {
        const fileDate = new Date(file.uploadedAt);
        if (fileDate < cutoffDate && file.type === "file") {
          await this.deleteFile(file.id);
          deletedCount++;
        }
      }

      return deletedCount;
    } catch (error) {
      console.error("Error cleaning up old files:", error);
      return 0;
    }
  },

  async getStorageBreakdown(userId: string): Promise<{
    totalFiles: number;
    totalSize: number;
    fileTypes: { [key: string]: { count: number; size: number } };
    cloudFiles: number;
    localFiles: number;
  }> {
    try {
      const files = this.getFiles(userId);
      const breakdown = {
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {} as { [key: string]: { count: number; size: number } },
        cloudFiles: 0,
        localFiles: 0,
      };

      files.forEach((file) => {
        if (file.type === "file") {
          breakdown.totalFiles++;
          breakdown.totalSize += file.size || 0;

          if (file.isCloudFile) {
            breakdown.cloudFiles++;
          } else {
            breakdown.localFiles++;
          }

          const fileType = file.mimeType || "unknown";
          if (!breakdown.fileTypes[fileType]) {
            breakdown.fileTypes[fileType] = { count: 0, size: 0 };
          }
          breakdown.fileTypes[fileType].count++;
          breakdown.fileTypes[fileType].size += file.size || 0;
        }
      });

      return breakdown;
    } catch (error) {
      console.error("Error getting storage breakdown:", error);
      return {
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {},
        cloudFiles: 0,
        localFiles: 0,
      };
    }
  },
};
