import { FileItem } from '../types';

interface CachedFile {
  fileId: string;
  content: string;
  cachedAt: string;
  lastAccessed: string;
}

interface PendingUpload {
  id: string;
  file: File;
  userId: string;
  parentId?: string;
  createdAt: string;
}

const CACHE_KEY = 'super_study_file_cache';
const PENDING_UPLOADS_KEY = 'super_study_pending_uploads';
const MAX_CACHE_SIZE = 50; // Maximum number of files to cache
const CACHE_EXPIRY_DAYS = 7; // Cache files for 7 days

export class OfflineSyncService {
  private isOnline: boolean = navigator.onLine;

  constructor() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  private handleOnline() {
    this.isOnline = true;
    console.log('App is online - syncing pending uploads...');
    this.syncPendingUploads();
  }

  private handleOffline() {
    this.isOnline = false;
    console.log('App is offline - caching enabled');
  }

  /**
   * Check if the app is currently online
   */
  getOnlineStatus(): boolean {
    return this.isOnline;
  }

  /**
   * Cache file content for offline access
   */
  cacheFile(fileId: string, content: string): void {
    try {
      const cache = this.getCache();
      const now = new Date().toISOString();

      // Remove existing entry if it exists
      const filteredCache = cache.filter(item => item.fileId !== fileId);

      // Add new entry
      const newCacheItem: CachedFile = {
        fileId,
        content,
        cachedAt: now,
        lastAccessed: now,
      };

      filteredCache.push(newCacheItem);

      // Sort by last accessed (most recent first) and limit cache size
      const sortedCache = filteredCache
        .sort((a, b) => new Date(b.lastAccessed).getTime() - new Date(a.lastAccessed).getTime())
        .slice(0, MAX_CACHE_SIZE);

      localStorage.setItem(CACHE_KEY, JSON.stringify(sortedCache));
    } catch (error) {
      console.error('Error caching file:', error);
    }
  }

  /**
   * Get cached file content
   */
  getCachedFile(fileId: string): string | null {
    try {
      const cache = this.getCache();
      const cachedFile = cache.find(item => item.fileId === fileId);

      if (!cachedFile) {
        return null;
      }

      // Check if cache is expired
      const cacheAge = Date.now() - new Date(cachedFile.cachedAt).getTime();
      const maxAge = CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000;

      if (cacheAge > maxAge) {
        this.removeCachedFile(fileId);
        return null;
      }

      // Update last accessed time
      cachedFile.lastAccessed = new Date().toISOString();
      this.updateCache(cache);

      return cachedFile.content;
    } catch (error) {
      console.error('Error getting cached file:', error);
      return null;
    }
  }

  /**
   * Remove a file from cache
   */
  removeCachedFile(fileId: string): void {
    try {
      const cache = this.getCache();
      const filteredCache = cache.filter(item => item.fileId !== fileId);
      localStorage.setItem(CACHE_KEY, JSON.stringify(filteredCache));
    } catch (error) {
      console.error('Error removing cached file:', error);
    }
  }

  /**
   * Clear all cached files
   */
  clearCache(): void {
    try {
      localStorage.removeItem(CACHE_KEY);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Add file to pending uploads queue (for offline uploads)
   */
  addPendingUpload(file: File, userId: string, parentId?: string): string {
    try {
      const pendingUploads = this.getPendingUploads();
      const uploadId = Date.now().toString(36) + Math.random().toString(36).substr(2);

      const pendingUpload: PendingUpload = {
        id: uploadId,
        file,
        userId,
        parentId,
        createdAt: new Date().toISOString(),
      };

      pendingUploads.push(pendingUpload);
      localStorage.setItem(PENDING_UPLOADS_KEY, JSON.stringify(pendingUploads));

      return uploadId;
    } catch (error) {
      console.error('Error adding pending upload:', error);
      throw error;
    }
  }

  /**
   * Get pending uploads
   */
  getPendingUploads(): PendingUpload[] {
    try {
      const stored = localStorage.getItem(PENDING_UPLOADS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting pending uploads:', error);
      return [];
    }
  }

  /**
   * Remove a pending upload
   */
  removePendingUpload(uploadId: string): void {
    try {
      const pendingUploads = this.getPendingUploads();
      const filtered = pendingUploads.filter(upload => upload.id !== uploadId);
      localStorage.setItem(PENDING_UPLOADS_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Error removing pending upload:', error);
    }
  }

  /**
   * Sync pending uploads when online
   */
  async syncPendingUploads(): Promise<void> {
    if (!this.isOnline) return;

    const pendingUploads = this.getPendingUploads();
    
    for (const upload of pendingUploads) {
      try {
        // Import storageUtils dynamically to avoid circular dependency
        const { storageUtils } = await import('./storage');
        
        const result = await storageUtils.uploadFileToCloud(
          upload.userId,
          upload.file,
          upload.parentId
        );

        if (result) {
          this.removePendingUpload(upload.id);
          console.log(`Successfully synced upload: ${upload.file.name}`);
        }
      } catch (error) {
        console.error(`Failed to sync upload ${upload.file.name}:`, error);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalFiles: number;
    totalSize: number;
    oldestFile: string | null;
    newestFile: string | null;
  } {
    const cache = this.getCache();
    
    let totalSize = 0;
    cache.forEach(item => {
      totalSize += item.content.length;
    });

    const sortedByDate = cache.sort((a, b) => 
      new Date(a.cachedAt).getTime() - new Date(b.cachedAt).getTime()
    );

    return {
      totalFiles: cache.length,
      totalSize,
      oldestFile: sortedByDate[0]?.cachedAt || null,
      newestFile: sortedByDate[sortedByDate.length - 1]?.cachedAt || null,
    };
  }

  private getCache(): CachedFile[] {
    try {
      const stored = localStorage.getItem(CACHE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting cache:', error);
      return [];
    }
  }

  private updateCache(cache: CachedFile[]): void {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.error('Error updating cache:', error);
    }
  }
}

export const offlineSyncService = new OfflineSyncService();
