import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  listAll,
  getMetadata,
  updateMetadata,
} from "firebase/storage";
import { storage } from "../config/firebase";

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  progress: number;
}

export interface CloudFileMetadata {
  name: string;
  size: number;
  contentType: string;
  timeCreated: string;
  updated: string;
  downloadURL: string;
  fullPath: string;
}

export interface UploadResult {
  success: boolean;
  downloadURL?: string;
  error?: string;
  metadata?: CloudFileMetadata;
}

export class CloudStorageService {
  private getUserPath(userId: string, path: string = ""): string {
    return `users/${userId}/files/${path}`.replace(/\/+/g, "/");
  }

  /**
   * Upload a file to Firebase Storage
   */
  async uploadFile(
    userId: string,
    file: File,
    path: string = "",
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      const fileName = `${Date.now()}_${file.name}`;
      const filePath = this.getUserPath(userId, `${path}/${fileName}`);
      const storageRef = ref(storage, filePath);

      const uploadTask = uploadBytesResumable(storageRef, file);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          "state_changed",
          (snapshot) => {
            const progress = {
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
              progress: (snapshot.bytesTransferred / snapshot.totalBytes) * 100,
            };
            onProgress?.(progress);
          },
          (error) => {
            console.error("Upload error:", error);
            resolve({
              success: false,
              error: error.message,
            });
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              const metadata = await getMetadata(uploadTask.snapshot.ref);
              
              resolve({
                success: true,
                downloadURL,
                metadata: {
                  name: metadata.name,
                  size: metadata.size,
                  contentType: metadata.contentType || file.type,
                  timeCreated: metadata.timeCreated,
                  updated: metadata.updated,
                  downloadURL,
                  fullPath: metadata.fullPath,
                },
              });
            } catch (error) {
              resolve({
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
              });
            }
          }
        );
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Download a file from Firebase Storage
   */
  async downloadFile(filePath: string): Promise<string | null> {
    try {
      const storageRef = ref(storage, filePath);
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.error("Download error:", error);
      return null;
    }
  }

  /**
   * Delete a file from Firebase Storage
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const storageRef = ref(storage, filePath);
      await deleteObject(storageRef);
      return true;
    } catch (error) {
      console.error("Delete error:", error);
      return false;
    }
  }

  /**
   * List all files for a user
   */
  async listUserFiles(userId: string, path: string = ""): Promise<CloudFileMetadata[]> {
    try {
      const userPath = this.getUserPath(userId, path);
      const storageRef = ref(storage, userPath);
      const result = await listAll(storageRef);

      const files: CloudFileMetadata[] = [];

      for (const itemRef of result.items) {
        try {
          const [downloadURL, metadata] = await Promise.all([
            getDownloadURL(itemRef),
            getMetadata(itemRef),
          ]);

          files.push({
            name: metadata.name,
            size: metadata.size,
            contentType: metadata.contentType || "application/octet-stream",
            timeCreated: metadata.timeCreated,
            updated: metadata.updated,
            downloadURL,
            fullPath: metadata.fullPath,
          });
        } catch (error) {
          console.error(`Error getting metadata for ${itemRef.fullPath}:`, error);
        }
      }

      return files;
    } catch (error) {
      console.error("List files error:", error);
      return [];
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(filePath: string): Promise<CloudFileMetadata | null> {
    try {
      const storageRef = ref(storage, filePath);
      const [downloadURL, metadata] = await Promise.all([
        getDownloadURL(storageRef),
        getMetadata(storageRef),
      ]);

      return {
        name: metadata.name,
        size: metadata.size,
        contentType: metadata.contentType || "application/octet-stream",
        timeCreated: metadata.timeCreated,
        updated: metadata.updated,
        downloadURL,
        fullPath: metadata.fullPath,
      };
    } catch (error) {
      console.error("Get metadata error:", error);
      return null;
    }
  }

  /**
   * Calculate storage usage for a user
   */
  async getUserStorageUsage(userId: string): Promise<number> {
    try {
      const files = await this.listUserFiles(userId);
      return files.reduce((total, file) => total + file.size, 0);
    } catch (error) {
      console.error("Get storage usage error:", error);
      return 0;
    }
  }

  /**
   * Check if user has exceeded storage quota
   */
  async checkStorageQuota(userId: string, maxBytes: number = 100 * 1024 * 1024): Promise<{
    usage: number;
    quota: number;
    exceeded: boolean;
    percentage: number;
  }> {
    const usage = await this.getUserStorageUsage(userId);
    const percentage = (usage / maxBytes) * 100;
    
    return {
      usage,
      quota: maxBytes,
      exceeded: usage > maxBytes,
      percentage,
    };
  }
}

export const cloudStorageService = new CloudStorageService();
