import React, { useState, useEffect } from "react";
import { HardDrive, <PERSON><PERSON><PERSON>riangle, CheckCircle } from "lucide-react";
import { storageUtils } from "../utils/storage";
import { realTimeAuth } from "../utils/realTimeAuth";

interface StorageQuotaProps {
  className?: string;
}

export const StorageQuota: React.FC<StorageQuotaProps> = ({ className = "" }) => {
  const [storageInfo, setStorageInfo] = useState<{
    usage: number;
    quota: number;
    exceeded: boolean;
    percentage: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  const user = realTimeAuth.getCurrentUser();

  useEffect(() => {
    if (user) {
      loadStorageInfo();
    }
  }, [user]);

  const loadStorageInfo = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const info = await storageUtils.getUserStorageUsage(user.id);
      setStorageInfo(info);
    } catch (error) {
      console.error("Error loading storage info:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusColor = () => {
    if (!storageInfo) return "bg-gray-200";
    
    if (storageInfo.exceeded) return "bg-red-500";
    if (storageInfo.percentage > 80) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStatusIcon = () => {
    if (!storageInfo) return <HardDrive className="w-4 h-4 text-gray-500" />;
    
    if (storageInfo.exceeded) return <AlertTriangle className="w-4 h-4 text-red-600" />;
    if (storageInfo.percentage > 80) return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    return <CheckCircle className="w-4 h-4 text-green-600" />;
  };

  const getStatusText = () => {
    if (!storageInfo) return "Loading...";
    
    if (storageInfo.exceeded) return "Storage quota exceeded";
    if (storageInfo.percentage > 80) return "Storage almost full";
    return "Storage healthy";
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-2 bg-gray-200 rounded w-full"></div>
        </div>
      </div>
    );
  }

  if (!storageInfo) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center text-gray-500">
          <HardDrive className="w-4 h-4 mr-2" />
          <span className="text-sm">Storage info unavailable</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          {getStatusIcon()}
          <span className="ml-2 text-sm font-medium text-gray-900">
            Cloud Storage
          </span>
        </div>
        <span className="text-xs text-gray-500">
          {formatBytes(storageInfo.usage)} / {formatBytes(storageInfo.quota)}
        </span>
      </div>
      
      <div className="mb-2">
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>{getStatusText()}</span>
          <span>{Math.round(storageInfo.percentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
            style={{ width: `${Math.min(storageInfo.percentage, 100)}%` }}
          ></div>
        </div>
      </div>
      
      {storageInfo.exceeded && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
          <strong>Storage limit exceeded!</strong> Please delete some files to continue uploading.
        </div>
      )}
      
      {storageInfo.percentage > 80 && !storageInfo.exceeded && (
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
          <strong>Storage almost full.</strong> Consider cleaning up old files.
        </div>
      )}
    </div>
  );
};
