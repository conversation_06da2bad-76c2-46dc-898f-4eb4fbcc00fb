/* Additional styles for swipeable task items */
.swipeable-task-item {
  touch-action: pan-y; /* Allow vertical scrolling but handle horizontal swipes */
  user-select: none; /* Prevent text selection during swipe */
  transition: transform 0.2s ease-out;
}

/* Hide scrollbars for filter tabs */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.swipeable-task-item:active {
  cursor: grabbing;
}

.swipeable-task-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Smooth transitions for swipe actions */
.swipe-action-bg {
  transition: opacity 0.2s ease-out;
}

/* Enhanced pulse animation for action feedback */
@keyframes swipe-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes completion-bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes sparkle-burst {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
  }
}

.swipe-action-active {
  animation: swipe-pulse 0.6s ease-in-out;
}

.task-completing {
  animation: completion-bounce 0.6s ease-in-out;
}

.task-completed {
  animation: glow-pulse 2s ease-in-out infinite;
}

.sparkle-effect {
  animation: sparkle-burst 0.8s ease-out forwards;
}

/* Prevent text selection during drag */
.swipeable-task-item * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Success state styling */
.task-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  transform: scale(1.02);
  transition: all 0.3s ease-out;
}

/* Priority-based glow effects */
.priority-high-glow {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
}

.priority-medium-glow {
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
}

.priority-low-glow {
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
}

/* Motivational text animations */
@keyframes text-celebration {
  0% {
    transform: scale(1);
    color: inherit;
  }
  50% {
    transform: scale(1.05);
    color: #10b981;
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}

.celebrating-text {
  animation: text-celebration 0.6s ease-in-out;
}

/* Re-enable text selection for content when not dragging */
.swipeable-task-item:not(.dragging) .task-content {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
